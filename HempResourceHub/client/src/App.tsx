import { lazy, Suspense, useEffect } from "react";
import { Switch, Route, useLocation } from "wouter";
import { queryClient } from "@/lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { ErrorBoundary } from "@/components/error-boundary";
import { AuthProvider } from "@/components/auth/auth-provider";
import { EnhancedAuthProvider } from "@/components/auth/enhanced-auth-provider";
import NotFound from "@/pages/not-found";
import { OptimizedParticleBackground } from "@/components/layout/OptimizedParticleBackground";
import { LayoutWrapper } from "@/components/layout/LayoutWrapper";
import LoadingSpinner from "@/components/ui/loading-spinner";
import { performanceMonitor } from "@/lib/performance-monitor";
import { useGlobalKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { KeyboardShortcutsModal } from "@/components/ui/keyboard-shortcuts-modal";

// Simple redirect component
function Redirect({ to }: { to: string }) {
  const [, setLocation] = useLocation();

  useEffect(() => {
    setLocation(to);
  }, [to, setLocation]);

  return null;
}

// Lazy load all route components
const HomePage = lazy(() => import("@/pages/home"));
const AboutPage = lazy(() => import("@/pages/about"));
const PlantPartsPage = lazy(() => import("@/pages/plant-parts"));
const PlantTypePage = lazy(() => import("@/pages/plant-type"));
const PlantPartPage = lazy(() => import("@/pages/plant-part"));
const ProductDetailPage = lazy(() => import("@/pages/product-detail-simplified"));
const IndustriesPage = lazy(() => import("@/pages/industries"));
const ResearchPage = lazy(() => import("@/pages/research"));
const ResearchDetailPage = lazy(() => import("@/pages/research-detail"));
const AllProductsPage = lazy(() => import("@/pages/all-products-simplified"));
const HempDexUnified = lazy(() => import("@/pages/hemp-dex-unified"));
const SupabaseTest = lazy(() => import("@/components/supabase-test"));
const SupabaseIndustries = lazy(() => import("@/components/supabase-industries"));
const SupabaseTestConnection = lazy(() => import("@/components/supabase-test-connection"));
const DebugSupabase = lazy(() => import("@/pages/debug-supabase"));
const AdminPage = lazy(() => import("@/pages/admin"));
const AdminDashboardRedesigned = lazy(() => import("@/pages/admin-dashboard-redesigned"));
const AdminSettings = lazy(() => import("@/pages/admin-settings"));
const LoginPage = lazy(() => import("@/pages/login"));
const RegisterPage = lazy(() => import("@/pages/register"));
const DashboardPage = lazy(() => import("@/pages/dashboard-spacing-enhanced"));
const AuthCallbackPage = lazy(() => import("@/pages/auth-callback"));
const HempCompanies = lazy(() => import("@/pages/hemp-companies-enhanced"));
const EnhancedSearchPage = lazy(() => import("@/pages/enhanced-search"));
const ProductsExplorerPage = lazy(() => import("@/pages/products-explorer"));
const ProductsDiscoveryPage = lazy(() => import("@/pages/products-discovery"));
const ProductsDataTablePage = lazy(() => import("@/pages/products-data-table"));
const ProductFormPage = lazy(() => import("@/pages/product-form-page"));
const TestNavigationPage = lazy(() => import("@/pages/test-navigation"));
const ProductShowcasePage = lazy(() => import("@/pages/product-showcase"));
const HempDexEnhanced = lazy(() => import("@/pages/hemp-dex-unified-enhanced"));
const HempDiscoveryHub = lazy(() => import("@/pages/hemp-discovery-hub"));
const EnhancedProductDetail = lazy(() => import("@/pages/enhanced-product-detail"));
const SitemapPage = lazy(() => import("@/pages/sitemap"));
const PrivacyPage = lazy(() => import("@/pages/privacy"));
const TermsPage = lazy(() => import("@/pages/terms"));
const ContactPage = lazy(() => import("@/pages/contact"));

function Router() {
  return (
    <Suspense fallback={<LoadingSpinner />}>
      <Switch data-oid="mz1m0k7">
        <Route path="/" component={HomePage} data-oid=".stzk2_" />
      <Route path="/dashboard" component={DashboardPage} data-oid="dashboard" />
      <Route path="/about" component={AboutPage} data-oid="8cc79-4" />
      <Route
        path="/plant-parts"
        component={PlantPartsPage}
        data-oid="p8o_m6_"
      />


      <Route
        path="/plant-type/:id"
        component={PlantTypePage}
        data-oid="-768tu8"
      />

      <Route
        path="/plant-part/:id"
        component={PlantPartPage}
        data-oid="wb_chhl"
      />

      {/* Redirect /plant-part (without ID) to /plant-parts */}
      <Route
        path="/plant-part"
        component={() => <Redirect to="/plant-parts" />}
      />

      <Route
        path="/product/:id"
        component={EnhancedProductDetail}
        data-oid="q_cf4tj"
      />

      {/* Redirect /product (without ID) to /products */}
      <Route
        path="/product"
        component={() => <Redirect to="/products" />}
      />

      <Route path="/industries" component={IndustriesPage} data-oid="i4mqmig" />
      <Route path="/hemp-companies" component={HempCompanies} data-oid="hemp-companies" />
      <Route path="/companies" component={() => <Redirect to="/hemp-companies" />} />
      {/* Products - Single consolidated route */}
      <Route path="/products" component={ProductsDiscoveryPage} data-oid="products" />
      <Route path="/products-explorer" component={ProductsExplorerPage} data-oid="products-explorer" />
      <Route path="/products-list" component={AllProductsPage} data-oid="products-list" />
      <Route path="/products-table" component={ProductsDataTablePage} data-oid="products-table" />
      <Route path="/products/new" component={ProductFormPage} data-oid="product-form" />
      <Route path="/hemp-dex-unified" component={HempDexUnified} data-oid="hemp-dex-unified" />
      <Route path="/hemp-dex-enhanced" component={HempDexEnhanced} data-oid="hemp-dex-enhanced" />
      <Route path="/hemp-discovery" component={HempDiscoveryHub} data-oid="hemp-discovery-hub" />
      <Route path="/search" component={EnhancedSearchPage} data-oid="enhanced-search" />
      <Route path="/test-navigation" component={TestNavigationPage} data-oid="test-navigation" />
      <Route path="/product-showcase" component={ProductShowcasePage} data-oid="product-showcase" />

      {/* Legacy redirects */}
      <Route path="/hemp-dex" component={() => { window.location.href = "/hemp-dex-unified"; return null; }} />
      <Route path="/hemp-dex-old" component={() => { window.location.href = "/products"; return null; }} />
      <Route path="/all-products" component={() => { window.location.href = "/products"; return null; }} />
      <Route path="/products-by-category" component={() => { window.location.href = "/hemp-dex-unified?tab=plant-parts"; return null; }} />
      <Route path="/product-listing" component={() => { window.location.href = "/products"; return null; }} />
      <Route path="/research" component={ResearchPage} data-oid="r71_flj" />
      <Route
        path="/research/:paperId"
        component={ResearchDetailPage}
        data-oid="foofk03"
      />

      <Route
        path="/supabase-test"
        component={SupabaseTest}
        data-oid="n.xojo2"
      />

      <Route
        path="/supabase-industries"
        component={SupabaseIndustries}
        data-oid="i7ewdxm"
      />

      <Route
        path="/supabase-connection"
        component={SupabaseTestConnection}
        data-oid="80q9r5w"
      />

      <Route path="/debug" component={DebugSupabase} data-oid="1-dlpt_" />
      <Route path="/debug-supabase" component={DebugSupabase} data-oid="debug-sup" />
      <Route path="/admin" component={AdminPage} data-oid="admin-page" />
      <Route path="/admin-dashboard" component={AdminDashboardRedesigned} data-oid="admin-dashboard" />
      <Route path="/admin-settings" component={AdminSettings} data-oid="admin-settings" />
      <Route path="/login" component={LoginPage} data-oid="login" />
      <Route path="/register" component={RegisterPage} data-oid="register" />
      <Route path="/auth/callback" component={AuthCallbackPage} data-oid="auth-callback" />
      
      {/* Legal & Support Pages */}
      <Route path="/sitemap" component={SitemapPage} data-oid="sitemap" />
      <Route path="/privacy" component={PrivacyPage} data-oid="privacy" />
      <Route path="/terms" component={TermsPage} data-oid="terms" />
      <Route path="/contact" component={ContactPage} data-oid="contact" />
      
      <Route component={NotFound} data-oid="94.5p89" />
      </Switch>
    </Suspense>
  );
}

function AppContent() {
  // Enable global keyboard shortcuts
  useGlobalKeyboardShortcuts();
  
  return (
    <>
      <div className="min-h-screen text-gray-100 relative" data-oid="9aq6v3f">
        <OptimizedParticleBackground quality="medium" />
        <LayoutWrapper>
          <Router data-oid="geelxlm" />
        </LayoutWrapper>
      </div>
      <KeyboardShortcutsModal />
    </>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient} data-oid="u-8-pob">
        <EnhancedAuthProvider>
          <TooltipProvider data-oid="0n1weth">
            <AppContent />
            <Toaster data-oid="yh70n6c" />
          </TooltipProvider>
        </EnhancedAuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
